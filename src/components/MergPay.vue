<!-- 合并支付 合并取消订单 -->
<template>
  <uni-popup type="bottom" class="isMergePopup" ref="isMergePopup">
    <view class="changeIsMergePopup-content">
      <view class="changeIsMergePopup-handle-card">
        <view
          class="changeIsMergePopup-handle-card-close"
          @click="closeIsMergePopup"
        >
          <uni-icons type="closeempty" size="22" color="#000000"></uni-icons>
        </view>
      </view>
      <view class="listCon">
        <view class="title_text">包含以下缴费项目，需要合并支付</view>
        <view class="list_box">
          <view
            class="list_item"
            v-for="(item, index) in state.unpaidOrderList"
          >
            <view class="list_item1">
              <view class="line_box"></view>
              <view class="list_item1_text">{{ item.title }}</view>
            </view>
            <view class="list_item2">
              <view>{{ item.title }}</view>
            </view>
            <view class="list_item3">
              <view>应缴金额：{{ item.payableAmount }}</view>
            </view>
            <view class="list_item4">
              <view>减免金额：{{ item.payableAmount - item.payAmount}}</view>
            </view>
            <view class="list_item5">
              实缴金额：<text class="amount">¥{{ item.payAmount }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="bottom-fixed">
        <view
          @click="payOrder"
          v-if="state.type === 'pay'"
          class="bottom-fixed_confirm-btn"
        >
          立即支付
        </view>
        <view
          @click="cancelOrder"
          v-if="state.type === 'nopay'"
          class="bottom-fixed_confirm-btn_no"
        >
          确定取消
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { reactive } from "vue";

import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";
const isMergePopup = ref(null);
const loginInfo = useSafeStorage("checkIn-loginInfo", {});
const state = reactive({
  type: null,
  tradeNo: null,
  schoolId: null,
  unpaidOrderList: [],
});
const showIsMergePopup = (item, type) => {
  state.type = type;
  state.tradeNo = item.tradeNo;
  state.schoolId = item.schoolId;
  isMergePopup.value.open();
  // 获取列表
  http
    .post("/campuspay/mobile/general-pay-center/unpaidTradeOrderList", {
      orderUserId: loginInfo.value.reportId,
      tradeNo: state.tradeNo,
      schoolId: state.schoolId,
    })
    .then((res) => {
      state.unpaidOrderList = res.data.unpaidOrderList;
      uni.setStorageSync("unpaidTradeOrderList", JSON.stringify(res.data));
    })
    .catch((err) => {});
};

// 处理缴费
const payOrder = () => {
  // 去订单支付页面付钱了
  uni.navigateTo({
    url: `/pages/checkIn/payMoney`,
  });
};

const cancelOrder = () => {
  http
    .post("/campuspay/mobile/general-pay-order/cancel/order", {
      tradeNo: state.tradeNo,
      orderUserId: loginInfo.value.reportId,
    })
    .then((res) => {
      uni.showToast({
        title: "取消订单成功",
        icon: "none",
        duration: 2000,
      });
      closeIsMergePopup();
    });
};

const closeIsMergePopup = () => {
  isMergePopup.value.close();
};
defineExpose({
  showIsMergePopup,
});
</script>

<style scoped>
.changeIsMergePopup-content {
  height: 800rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  position: relative;
  display: flex;
  flex-direction: column;
}

.changeIsMergePopup-handle-card {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.changeIsMergePopup-handle-card-close {
  padding: 30rpx 30rpx 0 30rpx;
}

.listCon {
  padding: 0rpx 60rpx 130rpx 60rpx;
  flex: 1;
  overflow: scroll;
}
.title_text {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  padding-bottom: 70rpx;
}

.list_item {
  border-bottom: 1px solid #ebebeb;
}

.list_item1 {
  display: flex;
  align-items: center;
  padding-bottom: 32rpx;
  padding-top: 32rpx;
}

.line_box {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
  margin-right: 8rpx;
}
.list_item1_text {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

.list_item2 {
  padding-bottom: 24rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
}
.list_item3 {
  padding-bottom: 24rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}
.list_item4 {
  padding-bottom: 24rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

.list_item5 {
  text-align: right;
  padding-bottom: 34rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

.list_item5 .amount {
  font-weight: 600;
  font-size: 36rpx;
  color: #333333;
  padding-left: 32rpx;
}

.bottom-fixed {
  position: absolute;
  bottom: 0px;
  width: 100%;
  background-color: #ffffff;
}
.bottom-fixed_confirm-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 92rpx;
  background: #00d190;
  border-radius: 46rpx;
  font-weight: 400;
  font-size: 32rpx;
  color: #ffffff;
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
}

.bottom-fixed_confirm-btn_no {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 92rpx;
  background: rgba(0, 209, 144, 0.09);
  border: 2rpx solid #00b781;
  border-radius: 46rpx;
  font-weight: 400;
  font-size: 32rpx;
  color: #00b781;
  margin-left: 30rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
}
</style>
