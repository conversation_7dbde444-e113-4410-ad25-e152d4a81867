<template>
  <view v-if="!isWechatEnv" class="wechat-check-container">
    <view class="wechat-check-content">
      <view class="icon-container">
        <image
          src="@/static/image/weixin.png"
          class="wechat-icon"
          mode="aspectFit"
        />
      </view>
      
      <view class="title">请在微信中打开</view>
      
      <view class="description">
        <text>请使用微信扫描二维码</text>
        <text>或在微信中打开此链接</text>
      </view>
      
      <view class="steps">
        <view class="step-item">
          <view class="step-number">1</view>
          <text class="step-text">打开微信</text>
        </view>
        <view class="step-item">
          <view class="step-number">2</view>
          <text class="step-text">扫描二维码或点击链接</text>
        </view>
        <view class="step-item">
          <view class="step-number">3</view>
          <text class="step-text">在微信内置浏览器中访问</text>
        </view>
      </view>
      
      <view class="tips">
        <text>如果您已在微信中，请尝试刷新页面</text>
      </view>
      
      <button class="refresh-btn" @click="refreshPage">
        刷新页面
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { isWechatBrowser } from '@/utils/wechat'

// 是否在微信环境中
const isWechatEnv = ref(true)

// 检测微信环境
const checkWechatEnv = () => {
  // #ifdef H5
  isWechatEnv.value = isWechatBrowser()
  // #endif
  
  // #ifndef H5
  // 非H5环境默认通过检测
  isWechatEnv.value = true
  // #endif
}

// 刷新页面
const refreshPage = () => {
  // #ifdef H5
  window.location.reload()
  // #endif
  
  // #ifndef H5
  // 非H5环境重新检测
  checkWechatEnv()
  // #endif
}

onMounted(() => {
  checkWechatEnv()
})
</script>

<style lang="scss" scoped>
.wechat-check-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.wechat-check-content {
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

.icon-container {
  margin-bottom: 60rpx;
}

.wechat-icon {
  width: 120rpx;
  height: 120rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 40rpx;
}

.description {
  font-size: 32rpx;
  color: #666666;
  line-height: 48rpx;
  margin-bottom: 80rpx;
  
  text {
    display: block;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.steps {
  margin-bottom: 80rpx;
}

.step-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-text {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
  text-align: left;
}

.tips {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 60rpx;
  line-height: 40rpx;
}

.refresh-btn {
  width: 400rpx;
  height: 80rpx;
  background-color: #07c160;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  
  &:active {
    background-color: #06ad56;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .wechat-check-content {
    padding: 0 20rpx;
  }
  
  .step-item {
    padding: 0 20rpx;
  }
}
</style>
