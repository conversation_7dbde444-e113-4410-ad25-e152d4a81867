<template>
  <view class="page-container">
    <view class="segmentation"> </view>
    <view class="form-container">
      <!-- 表单内容 -->
      <view class="form-content">
        <view class="form-item">
          <view class="label">
            <text class="label-text">姓名：</text>
          </view>
          <input
            class="form-input"
            disabled
            v-model="state.info.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">性别：</text>
          </view>
          <input
            class="form-input"
            disabled
            v-model="state.info.genderText"
            placeholder="请输入性别"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">手机号码</text>
          </view>
          <input
            class="form-input"
            disabled
            v-model="state.info.phone"
            placeholder="请输入手机号码"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">专业：</text>
          </view>
          <input
            class="form-input"
            disabled
            v-model="state.info.enrollmentMajorName"
            placeholder="请输入专业："
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive } from "vue";

const state = reactive({
  info: {},
});

onLoad((options) => {
  // 解析对象类型参数
  if (options.data) {
    // 直接从url上拿参数回来就行了 简单的死的参数
    const dataObj = JSON.parse(decodeURIComponent(options.data));
    state.info = dataObj;
    state.info.genderText = state.info.gender === 1 ? "男" : "女";
  }
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.segmentation {
  height: 20rpx;
  background-color: #f6f6f6;
}

.form-container {
  background-color: #ffffff;
  overflow: hidden;
}

.form-title {
  background-color: #f6f6f6;

  padding: 40rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}
</style>
