<template>
  <view class="page-container">
    <view class="segmentation"> </view>
    <view class="form-container">
      <!-- 表单内容 -->
      <view class="form-content">
        <view class="form-item">
          <view class="label">
            <text class="label-text">姓名：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="personalInfo.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">性别：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="genderText"
            placeholder="请输入性别"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">手机号码</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="personalInfo.phone"
            placeholder="请输入手机号码"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">专业：</text>
          </view>
          <input
            disabled
            class="form-input"
            v-model="selectSpecialty.majorName"
            placeholder="请输入专业："
          />
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">立即报名</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";

import useSignUpStore from "@/store/signUp";
// 使用 Pinia store 获取表单数据
const signUpStore = useSignUpStore();

// 从 Pinia store 获取数据
const personalInfo = computed(() => signUpStore.getPersonalInfo);

const genderText = computed(() => {
  return personalInfo.value.gender === 1 ? "男" : "女";
});

// 获取缓存中选中的专业对象
const selectSpecialty = useSafeStorage("selectSpecialty", {});

const handleSubmit = async () => {
  try {
    uni.showLoading({
      title: "提交中...",
    });

    await http.post("/app/enrollment/intention/createSignUp", {
      schoolId: selectSpecialty.value.schoolId,
      planId: selectSpecialty.value.planId,
      enrollmentMajorId: selectSpecialty.value.id,
      ...personalInfo.value,
        intentionTypeId: uni.getStorageSync("intentionTypeId") || null,
    });

    uni.hideLoading();

    // 提交成功
    uni.showToast({
      title: "提交成功",
      icon: "success",
      duration: 2000,
    });

    // 成功
    uni.navigateTo({
      url: "/pages/signUp/signInfoSuccess",
    });
  } catch (error) {
    uni.hideLoading();

    console.error("提交失败:", error);
  }
};
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.segmentation {
  height: 20rpx;
  background-color: #f6f6f6;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  overflow: hidden;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;

  padding: 40rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding-left: 40rpx;
  padding-right: 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #ff4757;
  font-size: 28rpx;
  margin-right: 4rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input::placeholder {
  color: #c0c0c0;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f6f6f6;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-weight: 400;
  font-size: 28rpx;

  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
