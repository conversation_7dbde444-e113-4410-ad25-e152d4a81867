<template>
  <view class="page-container">
    <view class="form-container" v-html="infos.content"></view>

    <view class="bottom-fixed">
      <button class="query-btn" @click="handleQuery">已了解，开始报名</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import http from "@/utils/request";

// 读取对象
const infoStr = uni.getStorageSync("info");
const infos = JSON.parse(infoStr);

const handleQuery = () => {
  uni.navigateTo({
    url: `/pages/signUp/specialty`,
  });
};
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 150rpx;
  padding-top: 20rpx;
  padding-right: 20rpx;
  padding-left: 20rpx;
}

.form-container {
  background-color: #f6f6f6;

  overflow: hidden;
}

.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f6f6f6;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.query-btn {
  width: 100%;
  height: 88rpx;
  background: #00b781;
  border: none;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff;
}
</style>
