<template>
  <view class="page-container">
    <image class="successImg" src="@/static/image/pic-tzs1-1.png"></image>

    <view class="title">录取通知书</view>

    <view class="textBox">
      <view class="name">{{ state.name }}你好：</view>

      <view class="schoolName"
        >根据<text>{{ state.schoolName }}</text
        >招生的有关政策，经审核、批准，你已被录取为我校</view
      >
      <view class="majorName"
        ><text>{{ state.enrollmentMajorName }}</text
        >专业的新生。请你持本通知书于<text
          >{{ state.reportStartDate }}至{{ state.reportEndDate }}</text
        >按时到校报到、注册。</view
      >
      <view class="time"
        >日期：<text>{{ state.sendTime }}</text></view
      >
    </view>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";

const state = reactive({
  name: "",
  schoolName: "",
  reportStartDate: "",
  reportEndDate: ""
});

onLoad((options) => {
  // 解析对象类型参数
  if (options.data) {
    // 直接从url上拿参数回来就行了 简单的死的参数
    const dataObj = JSON.parse(decodeURIComponent(options.data));
    console.log(dataObj);
    state.name = dataObj.name;
    state.schoolName = dataObj.schoolName;
    state.reportStartDate = dataObj.reportStartDate;
    state.reportEndDate = dataObj.reportEndDate;
    state.enrollmentMajorName = dataObj.enrollmentMajorName;
    state.sendTime = dataObj.sendTime;
  }
});
</script>

<style scoped>
.page-container {
  position: relative;
  height: 100vh;
  background: #f6f6f6;
  padding: 24rpx 18rpx;
}
.successImg {
  width: 100%;
}

.title {
  position: absolute;
  top: 48px;
  left: 135px;
  font-weight: 600;
  font-size: 44rpx;
  color: #b98304;
}
.textBox {
  font-weight: 500;
  font-size: 16rpx;
  position: absolute;
  top: 95px;
  left: 60px;
}
.name {
  padding-bottom: 18rpx;
  max-width: 280px;
  word-break: break-all;
}

.schoolName {
  padding-bottom: 24rpx;
  max-width: 280px;
  word-break: break-all;
  padding-left: 20px;
}

.majorName {
  padding-bottom: 24rpx;
  max-width: 280px;
  word-break: break-all;
}
.time {
  text-align: right;
}
</style>
