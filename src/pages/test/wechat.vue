<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">微信授权测试页面</text>
    </view>
    
    <view class="test-content">
      <view class="test-item">
        <text class="test-label">微信浏览器检测:</text>
        <text class="test-value" :class="{ success: isWechat, error: !isWechat }">
          {{ isWechat ? '✓ 在微信浏览器中' : '✗ 不在微信浏览器中' }}
        </text>
      </view>
      
      <view class="test-item">
        <text class="test-label">OpenID:</text>
        <text class="test-value" :class="{ success: openid, error: !openid }">
          {{ openid || '未获取到OpenID' }}
        </text>
      </view>
      
      <view class="test-item">
        <text class="test-label">URL参数:</text>
        <text class="test-value">{{ urlParams }}</text>
      </view>
    </view>
    
    <view class="test-actions">
      <button class="test-btn" @click="refreshData">刷新数据</button>
      <button class="test-btn" @click="clearOpenId">清除OpenID</button>
      <button class="test-btn" @click="testAuth">测试授权</button>
    </view>
    
    <view class="test-logs">
      <text class="logs-title">日志信息:</text>
      <view class="logs-content">
        <text v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { isWechatBrowser, getUrlParam, initWechatAuth } from '@/utils/wechat'
import { getOpenId, removeOpenId } from '@/utils/auth'

// 响应式数据
const isWechat = ref(false)
const openid = ref('')
const urlParams = ref('')
const logs = ref([])

// 添加日志
const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value.pop()
  }
}

// 刷新数据
const refreshData = () => {
  // #ifdef H5
  isWechat.value = isWechatBrowser()
  openid.value = getOpenId() || ''
  
  // 获取URL参数
  const params = new URLSearchParams(window.location.search)
  const paramObj = {}
  for (const [key, value] of params) {
    paramObj[key] = value
  }
  urlParams.value = Object.keys(paramObj).length > 0 ? JSON.stringify(paramObj) : '无参数'
  
  addLog('数据已刷新')
  // #endif
  
  // #ifndef H5
  isWechat.value = true // 非H5环境默认为true
  openid.value = getOpenId() || ''
  urlParams.value = '非H5环境'
  addLog('数据已刷新（非H5环境）')
  // #endif
}

// 清除OpenID
const clearOpenId = () => {
  removeOpenId()
  openid.value = ''
  addLog('OpenID已清除')
}

// 测试授权
const testAuth = async () => {
  try {
    addLog('开始测试微信授权...')
    await initWechatAuth()
    refreshData()
    addLog('微信授权测试完成')
  } catch (error) {
    addLog(`微信授权测试失败: ${error.message}`)
  }
}

onMounted(() => {
  refreshData()
  addLog('页面已加载')
})
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.test-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.test-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.test-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.test-value {
  font-size: 26rpx;
  color: #333333;
  word-break: break-all;
  
  &.success {
    color: #07c160;
  }
  
  &.error {
    color: #fa5151;
  }
}

.test-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  height: 80rpx;
  background-color: #07c160;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  
  &:active {
    background-color: #06ad56;
  }
}

.test-logs {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.logs-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.logs-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 36rpx;
  margin-bottom: 10rpx;
  padding: 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
