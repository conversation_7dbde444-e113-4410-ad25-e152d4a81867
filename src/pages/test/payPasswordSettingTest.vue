<template>
  <view class="test-page">
    <!-- 测试按钮区域 -->
    <view v-if="!showSetting" class="test-buttons">
      <view class="test-header">
        <text class="test-title">支付密码设置组件测试</text>
      </view>
      
      <view class="button-group">
        <button class="test-btn primary" @click="testNewPassword">
          测试新设置支付密码
        </button>
        
        <button class="test-btn secondary" @click="testModifyPassword">
          测试修改支付密码
        </button>
      </view>
      
      <view class="test-info">
        <view class="info-item">
          <text class="info-label">密码规则：</text>
          <text class="info-text">6位数字，不能有连续或重复的三个数字</text>
        </view>
        <view class="info-item">
          <text class="info-label">有效密码示例：</text>
          <text class="info-text">135792, 246810, 159357</text>
        </view>
        <view class="info-item">
          <text class="info-label">无效密码示例：</text>
          <text class="info-text">123456(连续), 111222(重复), 654321(连续)</text>
        </view>
      </view>
      
      <view v-if="lastResult" class="test-result">
        <text class="result-title">最后操作结果：</text>
        <text class="result-text" :class="lastResult.success ? 'success' : 'failed'">
          {{ lastResult.message }}
        </text>
      </view>
    </view>
    
    <!-- 支付密码设置组件 -->
    <PayPasswordSetting 
      v-if="showSetting"
      :set-password-function="mockSetPasswordAPI"
      :is-modify-mode="isModifyMode"
      @success="onSetPasswordSuccess"
      @failed="onSetPasswordFailed"
      @cancel="onSetPasswordCancel"
      @back="onSetPasswordBack"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import PayPasswordSetting from '@/components/PayPasswordSetting.vue'

// 响应式数据
const showSetting = ref(false)
const isModifyMode = ref(false)
const lastResult = ref(null)

/**
 * 测试新设置支付密码
 */
const testNewPassword = () => {
  isModifyMode.value = false
  showSetting.value = true
  lastResult.value = null
}

/**
 * 测试修改支付密码
 */
const testModifyPassword = () => {
  isModifyMode.value = true
  showSetting.value = true
  lastResult.value = null
}

/**
 * 模拟设置支付密码API
 * @param {string} password - 新密码
 * @returns {Promise<boolean>} 设置结果
 */
const mockSetPasswordAPI = async (password) => {
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟API调用结果（90%成功率）
    const success = Math.random() > 0.1
    
    if (success) {
      console.log('模拟API：密码设置成功', password)
      return true
    } else {
      console.log('模拟API：密码设置失败')
      return false
    }
    
  } catch (error) {
    console.error('模拟API：网络错误', error)
    return false
  }
}

/**
 * 设置成功回调
 * @param {string} password - 设置的密码
 */
const onSetPasswordSuccess = (password) => {
  lastResult.value = {
    success: true,
    message: `支付密码设置成功！密码：${password}`
  }
  
  showSetting.value = false
  
  uni.showToast({
    title: '密码设置成功',
    icon: 'success'
  })
  
  console.log('支付密码设置成功:', password)
}

/**
 * 设置失败回调
 * @param {string} errorMessage - 错误信息
 */
const onSetPasswordFailed = (errorMessage) => {
  lastResult.value = {
    success: false,
    message: `设置失败：${errorMessage}`
  }
  
  console.log('支付密码设置失败:', errorMessage)
}

/**
 * 取消设置回调
 */
const onSetPasswordCancel = () => {
  lastResult.value = {
    success: false,
    message: '用户取消了密码设置'
  }
  
  showSetting.value = false
  console.log('用户取消密码设置')
}

/**
 * 返回回调
 */
const onSetPasswordBack = () => {
  showSetting.value = false
  console.log('用户返回')
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.test-buttons {
  padding: 32rpx;
  
  .test-header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .test-title {
      font-size: 40rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .button-group {
    margin-bottom: 60rpx;
    
    .test-btn {
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      border-radius: 44rpx;
      border: none;
      margin-bottom: 32rpx;
      
      &.primary {
        background-color: #00B781;
        color: #ffffff;
        
        &:active {
          background-color: #00B781;
        }
      }
      
      &.secondary {
        background-color: #6c757d;
        color: #ffffff;
        
        &:active {
          background-color: #545b62;
        }
      }
    }
  }
  
  .test-info {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;
    
    .info-item {
      margin-bottom: 24rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        font-size: 28rpx;
        color: #666666;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .info-text {
        font-size: 26rpx;
        color: #333333;
        line-height: 1.5;
      }
    }
  }
  
  .test-result {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    
    .result-title {
      font-size: 28rpx;
      color: #666666;
      display: block;
      margin-bottom: 16rpx;
    }
    
    .result-text {
      font-size: 30rpx;
      font-weight: 500;
      
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
  }
}
</style>
