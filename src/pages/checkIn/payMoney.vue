<template>
  <view class="payment-container">
    <!-- 支付卡片 -->
    <view class="payment-card">
      <!-- 倒计时区域 -->
      <view class="countdown-section">
        <text class="countdown-text">支付剩余时间</text>
        <uni-countdown
          :show-day="false"
          :showHour="false"
          :minute="state.minute"
          :second="state.second"
          @timeup="timeup"
        />
      </view>
      <!-- 支付金额区域 -->
      <view class="amount-section">
        <text class="amount-symbol">¥</text>
        <text class="amount-value">{{ smsCheckPrepay.payAmountSum || 0 }}</text>
      </view>
      <!-- 订单信息区域 -->
      <view class="order-section">
        <view class="order-row">
          <text class="order-label">订单信息：</text>
          <text class="order-value">{{ orderInfo }}</text>
        </view>
      </view>
      <!-- 支付方式区域 -->
      <view
        v-if="payMethodList && payMethodList.length > 0"
        class="payment-methods-container"
      >
        <!-- 交通银行聚合支付 -->
        <view v-for="(item, index) in payMethodList" :key="index">
          <view v-if="item.payMethodId === 3" class="bocom-payment-section">
            <view class="payment-section-title">{{ item.payMethodName }}</view>

            <!-- 银行卡列表 -->
            <view
              v-if="item.bocomCardList && item.bocomCardList.length > 0"
              class="bank-card-list"
            >
              <view
                v-for="(card, cardIndex) in item.bocomCardList"
                :key="cardIndex"
                class="bank-card-item"
                :class="{
                  selected:
                    selectedPayMethod?.payMethodId === 3 &&
                    selectedCard?.id === card.id,
                }"
                @click="selectBocomCard(item, card)"
              >
                <view class="card-left">
                  <image
                    class="bank-icon"
                    :src="getBankIcon(card.bankCode)"
                    mode="aspectFit"
                  ></image>
                  <view class="card-info">
                    <text class="bank-name"
                      >{{ card.bankName }}借记卡({{
                        card.accountLastFour
                      }})</text
                    >
                  </view>
                </view>
                <view class="card-right">
                  <view
                    class="check-icon"
                    :class="{
                      checked:
                        selectedPayMethod?.payMethodId === 3 &&
                        selectedCard?.id === card.id,
                    }"
                  >
                    <text
                      class="check-text"
                      v-if="
                        selectedPayMethod?.payMethodId === 3 &&
                        selectedCard?.id === card.id
                      "
                      >✓</text
                    >
                  </view>
                </view>
              </view>
            </view>

            <!-- 添加银行卡按钮 -->
            <view class="add-bank-card" @click="addBankCard"> 添加银行卡 </view>
          </view>
        </view>

        <!-- 更多支付方式 -->
        <view v-if="otherPayMethods.length > 0" class="more-payment-section">
          <view class="payment-section-title">更多支付</view>

          <view class="other-payment-list">
            <view
              v-for="(method, methodIndex) in otherPayMethods"
              :key="methodIndex"
              class="payment-method-item"
              :class="{
                selected: selectedPayMethod?.payMethodId === method.payMethodId,
              }"
              @click="selectPaymentMethod(method)"
            >
              <view class="method-left">
                <image
                  class="payment-icon"
                  :src="getPaymentIcon(method.payMethodId)"
                  mode="aspectFit"
                ></image>
                <text class="method-text">{{ method.payMethodName }}</text>
              </view>
              <view class="method-right">
                <view
                  class="check-icon"
                  :class="{
                    checked:
                      selectedPayMethod?.payMethodId === method.payMethodId,
                  }"
                >
                  <text
                    class="check-text"
                    v-if="selectedPayMethod?.payMethodId === method.payMethodId"
                    >✓</text
                  >
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="no-payment-method">暂未配置收款信息，请联系老师</view>
    </view>

    <!-- 确认支付按钮 -->
    <view
      class="payment-button-section"
      v-if="payMethodList && payMethodList.length > 0"
    >
      <button
        class="confirm-payment-btn"
        :class="{ disabled: !canPay }"
        @click="publicPayment"
      >
        确认支付
      </button>
    </view>

    <!-- 支付密码验证弹窗 -->
    <PayPasswordVerify
      v-model:visible="showPasswordVerify"
      :verify-function="verifyPayPassword"
      @success="onPasswordVerifySuccess"
      @failed="onPasswordVerifyFailed"
      @cancel="onPasswordVerifyCancel"
    />
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import dayjs from "dayjs";
import http from "@/utils/request";
import { onLoad, onShow } from "@dcloudio/uni-app";
import usePayStore from "@/store/pay";
import { initWechatAuth } from "@/utils/wechat.js";

import { bankConfig } from "../my/bankConfig";
import { getOpenId } from "@/utils/auth";
import PayPasswordVerify from "@/components/PayPasswordVerify.vue";
import { useSafeStorage } from "@/hooks/useSafeStorage";
import RSA from "@/utils/rsa";
const payStore = usePayStore();
const getBankIcon = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.icon;
};

const showPasswordVerify = ref(false);

const unpaidTradeOrderList = useSafeStorage("unpaidTradeOrderList", {});

const orderInfo = computed(() => {
  const infos = unpaidTradeOrderList.value.infos || [];
  const infosText = infos.join("；");
  return infosText;
});

// 从 Pinia store 获取数据
const smsCheckPrepay = computed(() => payStore.getSmsCheckPrepay);

let payMethodList = ref([]);

const state = reactive({
  minute: 0,
  second: 0,
});

// 支付相关数据
const selectedPayMethod = ref(null); // 选中的支付方式
const selectedCard = ref(null); // 选中的银行卡（交行支付时使用）

// 支付状态
const paymentStatus = ref("pending"); // pending, processing, success, failed, timeout

// 计算属性 - 其他支付方式（非交行支付）
const otherPayMethods = computed(() => {
  return payMethodList.value.filter((item) => item.payMethodId !== 3);
});

// 计算属性 - 是否可以支付
const canPay = computed(() => {
  const hasSelectedMethod = selectedPayMethod.value !== null;
  const isProcessing = paymentStatus.value === "processing";

  // 如果选择的是交行支付，还需要选择银行卡
  if (selectedPayMethod.value?.payMethodId === 3) {
    return hasSelectedMethod && selectedCard.value !== null && !isProcessing;
  }

  return hasSelectedMethod && !isProcessing;
});

// 选择支付方式（非交行支付）
const selectPaymentMethod = (method) => {
  selectedPayMethod.value = method;
  selectedCard.value = null; // 清除银行卡选择

  uni.showToast({
    title: `已选择${method.payMethodName}`,
    icon: "none",
    duration: 1000,
  });
};

// 选择交行银行卡
const selectBocomCard = (method, card) => {
  selectedPayMethod.value = method;
  selectedCard.value = card;

  uni.showToast({
    title: `已选择${card.bankName}借记卡`,
    icon: "none",
    duration: 1000,
  });
};

// 添加银行卡
const addBankCard = () => {
  uni.navigateTo({
    url: "/pages/my/bankList",
  });
};

// 获取支付方式图标
const getPaymentIcon = (payMethodId) => {
  console.log(payMethodId, "payMethodId");
  const iconMap = {
    1: "/static/image/wxPay.png", // 微信支付
    2: "/static/image/wxPay.png", // 支付宝（暂时使用微信图标）
    // 可以根据需要添加更多支付方式图标
  };
  return iconMap[payMethodId] || "/static/image/wxPay.png";
};

// 处理支付
const handlePayment = async () => {
  if (!canPay.value) {
    if (countdown.value <= 0) {
      uni.showToast({
        title: "支付已超时",
        icon: "none",
      });
    } else if (!isWechatSelected.value) {
      uni.showToast({
        title: "请选择支付方式",
        icon: "none",
      });
    }
    return;
  }

  try {
    paymentStatus.value = "processing";

    // 显示支付加载状态
    uni.showLoading({
      title: "正在调起支付...",
    });

    // 调用微信支付
    await initiateWechatPayment();
  } catch (error) {
    paymentStatus.value = "failed";
    uni.hideLoading();

    console.error("支付失败:", error);

    uni.showModal({
      title: "支付失败",
      content: error.message || "支付过程中出现错误，请重试",
      showCancel: true,
      cancelText: "取消",
      confirmText: "重试",
      success: (res) => {
        if (res.confirm) {
          paymentStatus.value = "pending";
        }
      },
    });
  }
};

// 发起微信支付
const initiateWechatPayment = async () => {
  try {
    // 第一步：调用微信支付
    const paymentResult = await requestWechatPayment(orderData);

    // // 第二步：处理支付结果
    // await handlePaymentResult(paymentResult);
  } catch (error) {
    throw error;
  }
};

// 调用H5微信支付
const requestWechatPayment = (orderData) => {
  return new Promise((resolve, reject) => {
    // #ifdef H5
    // H5端处理（通常跳转到微信支付页面）
    if (orderData.mwebUrl) {
      // 跳转到微信H5支付页面
      window.location.href = orderData.mwebUrl;
      window.open(h5_url, "_blank");
    } else {
      reject(new Error("H5支付暂不支持"));
    }
    // #endif
  });
};

// // 处理支付结果
// const handlePaymentResult = async (paymentResult) => {
//   try {
//     uni.hideLoading();

//     if (paymentResult.status === "success") {
//       // 验证支付结果
//       const verifyResult = await verifyPaymentResult(paymentResult);

//       if (verifyResult.success) {
//         paymentStatus.value = "success";
//         stopCountdown();

//         // 支付成功
//         uni.showToast({
//           title: "支付成功",
//           icon: "success",
//           duration: 2000,
//         });

//         // 延迟跳转到成功页面
//         setTimeout(() => {
//           uni.redirectTo({
//             url: `/pages/payment-success/`,
//           });
//         }, 2000);
//       } else {
//         throw new Error("支付验证失败");
//       }
//     } else {
//       throw new Error("支付未完成");
//     }
//   } catch (error) {
//     paymentStatus.value = "failed";
//     throw error;
//   }
// };

// 获取商家配置的支付方式配置
const getPayMethodList = () => {
  const merchantId = smsCheckPrepay.value.merchantId;
  const schoolId = smsCheckPrepay.value.schoolId;
  const tradeNo = smsCheckPrepay.value.tradeNo;
  http
    .post("/campuspay/mobile/general-pay-center/payMethodList", {
      merchantId: merchantId,
      schoolId: schoolId,
      tradeNo: tradeNo
    })
    .then((res) => {
      payMethodList.value = res.data;

      if (payMethodList.value.some((item) => item.payMethodId === 1)) {
        // 如果支付方式中存在微信支付则要开始做微信网页授权的操作
        // 该函数传入公众号的appid 进行授权
        initWechatAuth();
      }
    });
};

// 倒计时时间到
const timeup = () => {
  paymentStatus.value = "timeout";
  uni.showModal({
    title: "提示",
    content: "支付超时，请重新验证支付!",
    showCancel: false,
    confirmColor: "#00B781",
    success: function (res) {
      if (res.confirm) {
        payStore.resetPaty();
        // 清除所有支付信息 回到 我要缴费页面 重新用手机获取验证码支付
        uni.redirectTo({
          url: `/pages/checkIn/startPay`,
        });
      }
    },
  });
};

function onBridgeReady(params) {
  WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
    console.log(res, "支付结果");
    if (res.err_msg == "get_brand_wcpay_request:ok") {
      console.log("支付成功");
      setTimeout(() => {
        // 清除缓存回首页去
        payStore.resetPaty();
        uni.removeStorageSync("unpaidTradeOrderList");

        uni.redirectTo({
          url: `/pages/home/<USER>
        });
      }, 1000);
    } else {
      console.log("支付失败");
    }
  });
}

// H5支付
const publicPayment = () => {
  if (!canPay.value) {
    if (!selectedPayMethod.value) {
      uni.showToast({
        title: "请选择支付方式",
        icon: "none",
      });
    } else if (
      selectedPayMethod.value.payMethodId === 3 &&
      !selectedCard.value
    ) {
      uni.showToast({
        title: "请选择银行卡",
        icon: "none",
      });
    }
    return;
  }

  // 构建支付参数
  let params = {
    tradeNo: smsCheckPrepay.value.tradeNo, // 交易单号
    payAmountSum: smsCheckPrepay.value.payAmountSum, // 缴费金额
    configTypeId: selectedPayMethod.value.configTypeId, // 配置类型ID
    payMethodId: selectedPayMethod.value.payMethodId, // 支付方式
    schoolId: smsCheckPrepay.value.schoolId, // 学校ID
  };

  //  如果是微信支付
  if (selectedPayMethod.value.payMethodId === 1) {
    params.payType = 1;
    params.paySource = "公众号";
    params.openid = getOpenId();
    http
      .post("/campuspay/mobile/general-pay-center/paySubmit", params)
      .then((res) => {
        console.log(
          res.data.resultInfo.jsApiResult,
          "支付参数.成功后唤起微信支付"
        );
        const obj = {
          ...res.data.resultInfo.jsApiResult,
          package: res.data.resultInfo.jsApiResult.packageStr,
        };
        onBridgeReady(obj);
      })
      .finally(() => {});
  }
  // 如果是交行支付，添加银行卡信息
  if (selectedPayMethod.value.payMethodId === 3 && selectedCard.value) {
    params.payType = 3;
    params.paySource = "银行";

    params.relatedId = selectedCard.value.relatedId;

    // 把校验支付密码的弹窗打开 让用户输入密码支付
    showPasswordVerify.value = true;
  }
};

// 生命周期钩子
onLoad((options) => {
  getPayMethodList(); // 获取支付方式配置
});

onShow(() => {
  const payEndTime = smsCheckPrepay.value.payEndTime; // 获取订单的最后截止提交时间
  const dayJspayEndTime = dayjs(payEndTime);
  const date = dayjs().format("YYYY-MM-DD HH:mm:ss"); // 获取当前时间
  // 如果订单的最后可支付时间大于当前时间 则订单超时了
  if (dayJspayEndTime.isBefore(date)) {
    return;
  }
  const time = dayjs(dayJspayEndTime.diff(date)).format("mm ss").split(" ");
  state.minute = parseInt(time[0]);
  state.second = parseInt(time[1]);
});

/**
 * 支付密码把钱付出去
 * @param {string} password - 输入的支付密码
 * @returns {Promise<boolean>} 验证结果
 */
const verifyPayPassword = async (password) => {
  try {
    // 构建支付参数
    let params = {
      tradeNo: smsCheckPrepay.value.tradeNo, // 交易单号
      payAmountSum: smsCheckPrepay.value.payAmountSum, // 缴费金额
      configTypeId: selectedPayMethod.value.configTypeId, // 配置类型ID
      payMethodId: selectedPayMethod.value.payMethodId, // 支付方式
      schoolId: smsCheckPrepay.value.schoolId, // 学校ID
      payType: 666,
      paySource: "银行",
      relatedId: selectedCard.value.relatedId,
      passwordEncipher: RSA.encrypt(JSON.stringify(password)), // 支付密码加密
    };

    const res = await http.post(
      "/campuspay/mobile/general-pay-center/paySubmit",
      params
    );

    return res.data;
  } catch (error) {
    console.error("支付密码验证失败:", error);
    return false;
  }
};

/**
 * 支付密码验证成功回调
 * @param {string} password - 验证成功的密码
 */
const onPasswordVerifySuccess = (password) => {
  uni.showToast({
    title: "支付成功",
    icon: "none",
  });
};

/**
 * 支付密码验证失败回调
 * @param {string} errorMessage - 错误信息
 */
const onPasswordVerifyFailed = (errorMessage) => {
  console.log("支付密码验证失败:", errorMessage);
  uni.showToast({
    title: errorMessage,
    icon: "none",
  });
};

/**
 * 支付密码验证取消回调
 */
const onPasswordVerifyCancel = () => {
  console.log("用户取消支付密码验证");
};
</script>

<style scoped>
/* 页面容器 */
.payment-container {
  height: 100vh;
  background-color: #f5f5f5;

  padding: 80rpx 30rpx 160rpx 30rpx;
  display: flex;
  flex-direction: column;
}

/* 支付卡片 */
.payment-card {
  flex: 1;
}

/* 倒计时区域 */
.countdown-section {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-bottom: 60rpx;
}

.countdown-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}

/* 支付金额区域 */
.amount-section {
  text-align: center;
  margin-bottom: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-symbol {
  font-size: 48rpx;
  color: #333333;
  font-weight: 400;
  margin-right: 8rpx;
  margin-top: -10rpx;
}

.amount-value {
  font-size: 96rpx;
  color: #333333;

  line-height: 1;

  font-weight: 600;

  color: #333333;
}

/* 订单信息区域 */
.order-section {
  margin-bottom: 24rpx;
  padding: 38rpx 24rpx;

  background: #ffffff;
  border-radius: 12rpx;
}

.order-row {
  display: flex;
  align-items: flex-start;
}

.order-label {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.order-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
}

/* 支付方式容器 */
.payment-methods-container {
  margin-top: 24rpx;
}

/* 支付区域标题 */
.payment-section-title {
  margin-bottom: 24rpx;
  padding-left: 4rpx;
  font-weight: 400;
  font-size: 30rpx;
  color: #999999;
}

/* 交通银行聚合支付区域 */
.bocom-payment-section {
  margin-bottom: 40rpx;
}

/* 银行卡列表 */
.bank-card-list {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.bank-card-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

/* .bank-card-item:last-child {
  border-bottom: none;
} */

.bank-card-item.selected {
  background-color: #f8fffe;
}

.bank-card-item:active {
  background-color: #f8f9fa;
}

.card-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.bank-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
  border-radius: 8rpx;
}

.card-info {
  flex: 1;
}

.bank-name {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.card-right {
  display: flex;
  align-items: center;
}

/* 添加银行卡 */
.add-bank-card {
  display: flex;
  align-items: center;

  padding: 24rpx;
  background: #ffffff;
  border-radius: 0rpx 0 12rpx 12rpx;

  transition: all 0.3s ease;
}

.add-bank-card:active {
  background-color: #f8f9fa;
  border-color: #00b781;
}

/* 更多支付方式区域 */
.more-payment-section {
  margin-bottom: 40rpx;
}

.other-payment-list {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.payment-method-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.payment-method-item:last-child {
  border-bottom: none;
}

.payment-method-item.selected {
  background-color: #f8fffe;
}

.payment-method-item:active {
  background-color: #f8f9fa;
}

.method-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.payment-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.method-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.method-right {
  display: flex;
  align-items: center;
}

/* 选择图标 */
.check-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.check-icon.checked {
  background-color: #00d190;
  border-color: #00d190;
}

.check-text {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 确认支付按钮区域 */
.payment-button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.confirm-payment-btn {
  width: 100%;
  height: 80rpx;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
}

.confirm-payment-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

/* 动画效果 */
.payment-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 支付处理中的加载状态 */
.confirm-payment-btn.processing {
  background-color: #999999;
  pointer-events: none;
}

.no-payment-method {
  font-weight: 600;
  font-size: 28rpx;
  color: #00b781;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
</style>
