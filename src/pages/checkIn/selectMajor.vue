<template>
  <view class="page-container">
    <!-- 滚动容器 -->
    <scroll-view
      scroll-y="true"
      class="scroll-container"
      :show-scrollbar="false"
      enhanced
    >
      <view class="major-list">
        <view
          class="major-card"
          :class="{ selected: item.selected }"
          v-for="(item, index) in majorList"
          :key="item.id"
          @click.stop="lookMajor(item)"
        >
          <view
            class="select-indicator"
            v-if="item.selected"
            @click.stop="selectMajor(index)"
          >
            <uni-icons
              class="select-icon"
              type="checkmarkempty"
              size="16"
            ></uni-icons>
          </view>

          <view
            @click.stop="selectMajor(index)"
            class="select-indicator_no"
            v-if="!item.selected"
          >
          </view>

          <!-- 专业名称 -->
          <view class="major-name">{{ item.majorName }}</view>

          <!-- 专业信息 -->
          <view class="major-info"> 简介：{{ item.majorIntroduction }} </view>
        </view>
      </view>

      <!-- 底部留白，避免被固定按钮遮挡 -->
      <view class="bottom-space"></view>
    </scroll-view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button
        class="next-btn"
        :class="{ disabled: !hasSelectedMajor }"
        @click="goNext"
      >
        下一步
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { onLoad, onReady } from "@dcloudio/uni-app";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";

const majorList = ref([]);

const hasSelectedMajor = computed(() =>
  majorList.value.some((item) => item.selected)
);

const selectedMajors = computed(() =>
  majorList.value.find((item) => item.selected)
);

const selectMajor = (index) => {
  uni.vibrateShort(); // 短震动

  majorList.value[index].selected = !majorList.value[index].selected;
  // 只能单选一个专业
  majorList.value.forEach((item, i) => {
    if (i !== index) {
      item.selected = false;
    }
  });

  console.log(majorList.value[index].selected, "majorList");

  const action = majorList.value[index].selected ? "选中" : "取消选中";
  uni.showToast({
    title: `${action}：${majorList.value[index].majorName}`,
    icon: "none",
    duration: 1500,
  });
};

const lookMajor = (item) => {
  uni.setStorageSync("checkIn-majorIntroduce", JSON.stringify(item));
  uni.navigateTo({
    url: "/pages/checkIn/majorIntroduce",
  });
};

const goNext = () => {
  if (!hasSelectedMajor.value) {
    uni.showToast({
      title: "请先选择专业",
      icon: "none",
    });
    return;
  }
  uni.setStorageSync(
    "checkIn-selectSpecialty",
    JSON.stringify(selectedMajors.value)
  );
  uni.setStorageSync(
    "checkIn-majorIntroduce",
    JSON.stringify(selectedMajors.value)
  );
  uni.navigateTo({
    url: "/pages/checkIn/personalDetails",
  });
};

const getMajorList = async () => {
  try {
    const res = await http.post("/app/enrollment/major/majorList", {
      id: uni.getStorageSync("planId") || null,
    });
    majorList.value = res.data;
  } catch (error) {
    console.log(error, "error");
  }
};

onLoad(() => {
  getMajorList();
  setTimeout(() => {
    uni.showToast({
      title: "点击卡片右上角圆圈可选择专业",
      icon: "none",
      duration: 2000,
    });
  }, 1000);
});

onShow(() => {
  const majorIntroduce = useSafeStorage("checkIn-majorIntroduce", {});
  // 合并对象 如果缓存中的majorIntroduce 的selected 值为true则majorList数组中的这个item更新为true 其他设置为false

  console.log(majorIntroduce.value, "77777");

  if (majorIntroduce.value.selected) {
    majorList.value.forEach((item) => {
      if (item.majorId === majorIntroduce.value.majorId) {
        item.selected = true;
      } else {
        item.selected = false;
      }
    });
  }

  console.log(majorList.value, "234324223423");
});

onReady(() => {
  const schoolName = uni.getStorageSync("schoolName") || "";
  const planName = uni.getStorageSync("planName") || "";
  uni.setNavigationBarTitle({
    title: `${schoolName}${planName}`,
  });
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
}

/* 滚动容器 */
.scroll-container {
  flex: 1;
  /* padding: 24rpx; */
}

/* 专业列表 - Grid布局 */
.major-list {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列等宽 */
  gap: 24rpx; /* 行列间距都是24rpx */
}

/* 专业卡片 */
.major-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.2s ease;
  border: 2rpx solid transparent;
  user-select: none; /* 防止文字选中 */
  overflow: hidden;
}

/* 选中状态 */
.major-card.selected {
  border-color: #00b781;
}

/* 选中指示器 */
.select-indicator {
  position: absolute;
  top: 3rpx;
  right: 2rpx;
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #00b781;
  background-color: #00b781;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-indicator_no {
  position: absolute;
  top: 3rpx;
  right: 2rpx;
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #666666;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-icon {
  color: #ffffff !important;
  /* font-size: 14rpx; */
}

/* 专业名称 */
.major-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 44rpx;
  transition: color 0.2s ease;
  /* 专业名称单行显示 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 专业信息 */
.major-info {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 显示省略号来代表被修剪的文本 */
}

/* 底部留白 */
.bottom-space {
  height: 120rpx;
}

/* 固定底部 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f6f6f6;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

/* 下一步按钮 */
.next-btn {
  width: 100%;
  height: 80rpx;
  background-color: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.next-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}
</style>
