<template>
  <view class="page-container">
    <view class="page_line"></view>
    <!-- 缴费列表 -->
    <view class="payment-list">
      <view
        class="payment-item"
        v-for="(item, index) in paymentList"
        :key="item.id"
      >
        <!-- 缴费标题 -->
        <view class="payment-title">{{ item.title }}</view>

        <!-- 缴费详情 -->
        <view class="payment-details">
          <!-- 缴费时间 -->
          <view class="detail-row">
            <text class="detail-label">缴费时间</text>
            <text class="detail-value">{{ item.payEndTime }}</text>
          </view>

          <!-- 缴费细项 -->
          <view class="detail-row">
            <text class="detail-label">缴费细项</text>
            <text class="detail-value">{{ item.title }}</text>
          </view>

          <view class="detail-row">
            <text class="detail-label">应缴金额 </text>
            <text class="detail-value">{{ item.payableAmount }}</text>
          </view>

          <view class="detail-row">
            <text class="detail-label">减免金额 </text>
            <text class="detail-value">{{
              item.payableAmount - item.payAmount
            }}</text>
          </view>

          <!-- 缴费金额 -->
          <view class="detail-row">
            <text class="detail-label">缴费金额 </text>
            <text class="detail-value amount">¥{{ item.payAmount }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部固定区域 -->
    <view class="bottom-fixed">
      <!-- 合计金额 -->
      <view class="total-section">
        <text class="total-label">合计：</text>
        <text class="total-amount">¥{{ payAmountSum || 0 }}</text>
      </view>

      <!-- 立即缴费按钮 -->
      <button
        class="payment-btn"
        :class="{ disabled: !canPay }"
        @click="handlePayment"
      >
        立即缴费
      </button>
    </view>
  </view>
</template>

<script setup>
import http from "@/utils/request";
import { ref, computed, nextTick } from "vue";

import usePayStore from "@/store/pay";

let reportId = uni.getStorageSync("saveReportId") || "";

import { useSafeStorage } from "@/hooks/useSafeStorage";

const loginInfo = useSafeStorage("checkIn-loginInfo", {});

// 使用 Pinia store 管理表单数据
const payStore = usePayStore();

// 从 Pinia store 获取数据
const smsCheckPrepay = computed(() => payStore.getSmsCheckPrepay);

let payAmountSum = ref(0);

// const onRestore = () => {
//   console.log("下拉刷新复位");
// };

// const toggleActive = (item, index) => {
//   paymentList.value.forEach((item, i) => {
//     if (i === index) {
//       item.isActive = !item.isActive;
//     } else {
//       item.isActive = false;
//     }
//   });
// };

// 缴费列表数据
const paymentList = ref([]);

// 计算总金额
const totalAmount = computed(() => {
  const total = paymentList.value.reduce((sum, item) => {
    return sum + item.numericAmount;
  }, 0);
  return total.toFixed(2);
});

// 是否可以缴费
const canPay = computed(() => {
  return paymentList.value.length > 0 && payAmountSum.value > 0;
});

// 处理缴费
const handlePayment = () => {
  if (!canPay.value) {
    uni.showToast({
      title: "暂无可缴费项目",
      icon: "none",
    });
    return;
  }

  // 去订单支付页面付钱了
  uni.navigateTo({
    url: `/pages/checkIn/payMoney`,
  });
};

// 常规支付 - 查询未支付列表
const unpaidTradeOrderList = () => {
  return new Promise((resolve, reject) => {
    http
      .post("/campuspay/mobile/general-pay-center/unpaidTradeOrderList", {
        orderUserId: smsCheckPrepay.value.orderUserId,
        tradeNo: smsCheckPrepay.value.tradeNo,
        schoolId: smsCheckPrepay.value.schoolId,
      })
      .then((res) => {
        console.log(res, "拿到了可进行支付的列表信息进行渲染");
        paymentList.value = res.data.unpaidOrderList;
        payAmountSum.value = res.data.payAmountSum;
        uni.setStorageSync("unpaidTradeOrderList", JSON.stringify(res.data));
        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
};

// 生命周期钩子
onLoad((options) => {
  unpaidTradeOrderList();
});

onPullDownRefresh(() => {
  unpaidTradeOrderList()
    .then(() => {
      uni.showToast({
        title: "刷新成功",
        icon: "success",
      });
      uni.stopPullDownRefresh();
    })
    .catch(() => {
      uni.showToast({
        title: "刷新失败",
        icon: "none",
      });
      uni.stopPullDownRefresh();
    });
});

onBackPress(() => {
  uni.navigateTo({
    url: "/pages/home/<USER>",
  });
  return true;
});
</script>

<style scoped>
/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: 200rpx;
}

.page_line {
  height: 20rpx;
  background-color: #f6f5f8;
}

/* 缴费列表 */
.payment-list {
  padding: 0rpx 20rpx 0rpx 20rpx;
}

/* 缴费项目 */
.payment-item {
  background-color: #ffffff;
  padding: 30rpx 20rpx 52rpx 20rpx;

  border-bottom: 2rpx solid #ebebeb;
}

.payment-item.active {
  opacity: 0.9;
  background-color: #11c685;
}

/* 缴费标题 */
.payment-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #4d4d4d;

  margin-bottom: 30rpx;
}

/* 缴费详情 */
.payment-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 详情行 */
.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40rpx;
}

/* 详情标签 */
.detail-label {
  font-weight: 600;
  font-size: 24rpx;
  color: #464646;
}

/* 详情值 */
.detail-value {
  font-weight: 600;
  font-size: 24rpx;
  color: #464646;
  text-align: right;
}

/* 金额样式 */
.detail-value.amount {
  font-weight: 600;
  font-size: 40rpx;
  color: #000000;
}

/* 底部固定区域 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  /* box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1); */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 合计区域 */
.total-section {
  display: flex;
  align-items: center;
}

.total-label {
  font-weight: 600;
  font-size: 24rpx;
  color: #00b781;

  margin-right: 8rpx;
}

.total-amount {
  font-weight: 600;
  font-size: 40rpx;
  color: #00b781;
}

/* 立即缴费按钮 */
.payment-btn {
  width: 200rpx;
  height: 72rpx;
  background-color: #11c685;
  color: #ffffff;
  border: none;
  border-radius: 10rpx;
  font-weight: 400;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.payment-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
}
</style>
