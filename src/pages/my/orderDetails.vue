<template>
  <view class="order-record-page">
    <!-- z-paging组件，铺满全屏 -->
    <z-paging
      ref="pagingRef"
      v-model="orderList"
      :fixed="true"
      :auto="true"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="false"
      @query="queryOrderList"
    >
      <!-- 顶部固定区域：搜索和筛选 -->
      <template #bottom>
        <view class="bottomBtn">
          <!-- 只有待支付和支付中的有取消订单按钮 -->
          <view
            @click="cancelOrder(orderList[0])"
            v-if="
              orderList[0].orderStatus === 0 || orderList[0].orderStatus === 1
            "
            class="cancelBtn"
            >取消订单</view
          >
          <view
            @click="delOrder(orderList[0])"
            v-if="
              orderList[0].orderStatus === 2 ||
              orderList[0].orderStatus === 5 ||
              orderList[0].orderStatus === 6 ||
              orderList[0].orderStatus === 7
            "
            class="cancelBtn"
            >删除订单</view
          >
          <view
            @click="payOrder(orderList[0])"
            v-if="
              orderList[0].orderStatus === 0 || orderList[0].orderStatus === 1
            "
            class="payBtn"
            >立即支付</view
          >
          <view
            @click="refund(orderList[0])"
            v-if="orderList[0].orderStatus === 2"
            class="payBtn"
            >申请退款</view
          >
          <view
            v-if="
              orderList[0].orderStatus === 7 || orderList[0].orderStatus === 5
            "
            class="payBtn"
            @click="refundDetails(orderList[0])"
            >退款详情</view
          >
        </view>
      </template>
      <!-- 订单卡片列表 -->
      <view class="order-card-list">
        <view class="order-card" v-for="item in orderList" :key="item.id">
          <view class="order-card_box">
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-type">
                <text class="type-icon"></text>
                <text class="type-text">{{ item.title }}</text>
              </view>
              <view>
                <!-- 支付的状态 -->
                <view
                  style="padding-bottom: 8rpx"
                  class="order-status"
                  :style="{ color: getStatus(item.orderStatus).color }"
                >
                  {{ getStatus(item.orderStatus).text }}
                </view>
                <!-- 退款相关的状态 -->
                <view
                  class="order-status"
                  :style="{ color: getRefundStatus(item.orderStatus).color }"
                  v-if="getRefundStatus(item.orderStatus)"
                >
                  {{ getRefundStatus(item.orderStatus).text }}</view
                >
              </view>
            </view>

            <!-- 订单标题 -->
            <view class="order-title">{{ item.title }}</view>

            <!-- 订单标题 -->
            <view class="order-time">缴费时间: {{ item.payEndTime }}</view>

            <!-- 订单详情 -->
            <view class="order-details">
              <view class="detail-row">
                <text class="detail-label">缴费细项:</text>
                <text class="detail-value">{{ item.title || "-" }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">缴费明细:</text>
                <text class="detail-value">{{ item.title || "-" }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">应缴金额:</text>
                <text class="detail-value">{{
                  item.payableAmount || "-"
                }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">减免金额:</text>
                <text class="detail-value">{{
                  item.payableAmount - item.payAmount || "-"
                }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">实缴金额:</text>
                <text class="detail-value amount">¥{{ item.payAmount }}</text>
              </view>
            </view>
          </view>

          <view class="bgLine"></view>
          <view class="payee">
            <view class="payee_order-card">
              <!-- 订单详情 -->
              <view class="payee_order-details">
                <view class="payee_detail-row">
                  <text class="payee_detail-label">收款方:</text>
                  <text class="payee_detail-value">{{
                    item.merchantName
                  }}</text>
                </view>
                <view class="payee_detail-row">
                  <text class="payee_detail-label">内部订单编号:</text>
                  <text class="payee_detail-value">{{ item.orderNo }}</text>
                </view>
                <view class="payee_detail-row">
                  <text class="payee_detail-label">交易流水号:</text>
                  <text class="payee_detail-value">{{
                    item.transactionId
                  }}</text>
                </view>
                <view class="payee_detail-row">
                  <text class="payee_detail-label">创建时间:</text>
                  <text class="payee_detail-value">{{ item.createTime }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>

    <MergPay ref="isMergePopup"></MergPay>
    <uni-popup type="bottom" class="popup" ref="popup">
      <view class="popup-content">
        <view class="handle-card">
          <view class="handle-card-close" @click="closerefundDetails">
            <uni-icons type="closeempty" size="22" color="#000000"></uni-icons>
          </view>
        </view>
        <view class="card-List">
          <view class="cardItem">
            <view class="cardItem_label">退款原因</view>
            <view class="cardItem_value">{{
              state.refundDetails.refundReason
            }}</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款金额</view>
            <view class="cardItem_value"
              >¥{{ state.refundDetails.refundAmount }}</view
            >
          </view>
          <view class="cardItem">
            <view class="cardItem_label">退款编号</view>
            <view class="cardItem_value">{{
              state.refundDetails.refundNo
            }}</view>
          </view>
          <view class="cardItem">
            <view class="cardItem_label">申请退款时间</view>
            <view class="cardItem_value">{{
              state.refundDetails.createTime
            }}</view>
          </view>
          <view class="cardItem" v-if="state.refundDetails.orderStatus === 5">
            <view class="cardItem_label">退款失败原因</view>
            <view class="cardItem_value">{{
              state.refundDetails.createTime
            }}</view>
          </view>
          <view class="cardItem" v-if="state.refundDetails.orderStatus === 4">
            <view class="cardItem_label">退款到账时间</view>
            <view class="cardItem_value">{{
              state.refundDetails.refundTime
            }}</view>
          </view>

          <view class="cardItem" v-if="state.refundDetails.orderStatus === 4">
            <view class="cardItem_label">退款去向</view>
            <view class="cardItem_value">{{
              state.refundDetails.refundDestination
            }}</view>
          </view>
        </view>

        <view class="bottom_btn">
          <view
            @click="cancelRefund(state.refundDetails)"
            v-if="state.refundDetails.orderStatus === 0"
            class="bottom_del_btn"
            >取消申请</view
          >
          <view
            @click="delRefundDetails(state.refundDetails)"
            v-else
            class="bottom_del_btn"
            >删除记录</view
          >
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { ref, reactive, onMounted } from "vue";
import http from "@/utils/request";
import MergPay from "@/components/MergPay.vue";
import { onShow } from "@dcloudio/uni-app";

const popup = ref(null);
const isMergePopup = ref(null);
const detailId = ref("");

const state = reactive({
  refundDetails: {},
});

const refundDetails = (item) => {
  popup.value.open();
  // 获取退款详情
  http
    .post("/campuspay/mobile/general-pay-order/refund/details", {
      id: item.id,
    })
    .then((res) => {
      state.refundDetails = res.data;
    });
};
const orderList = ref([]);

const closerefundDetails = () => {
  popup.value.close();
};
const refundStatusMap = {
  7: {
    text: "退款成功",
    color: "#00D190",
  },
  3: {
    text: "审核中",
    color: "#FAAD14",
  },
  5: {
    text: "退款失败",
    color: "#FF1C1C",
  },
};

const getRefundStatus = (orderStatus) => {
  return refundStatusMap[orderStatus];
};

const statusMap = {
  0: {
    text: "待支付",
    color: "#FF7F00",
  },
  1: {
    text: "待支付",
    color: "#FF7F00",
  },
  2: {
    text: "已支付",
    color: "#00D190",
  },
  3: {
    text: "已支付",
    color: "#00D190",
  },
  4: {
    text: "已支付",
    color: "#00D190",
  },
  5: {
    text: "已支付",
    color: "#00D190",
  },
  6: {
    text: "已关闭",
    color: "#999999",
  },
  7: {
    text: "已关闭",
    color: "#999999",
  },
};

const getStatus = (orderStatus) => {
  return statusMap[orderStatus];
};

const pagingRef = ref(null);

// 查询订单列表
const queryOrderList = async () => {
  try {
    const response = await http.post(
      "/campuspay/mobile/general-pay-order/details",
      {
        id: detailId.value,
      }
    );
    let listArr = [];

    listArr.push(response.data);
    console.log(listArr, "订单详情");

    orderList.value = listArr;

    // 使用z-paging的complete方法完成数据加载
    pagingRef.value.complete(listArr);
  } catch (error) {
    console.error("查询订单列表失败:", error);
    pagingRef.value.complete([]);
  }
};

// @refresherrefresh 下拉刷新时触发
const onRefresh = () => {
  console.log("下拉刷新,");
  // 可以在这里做一些额外的刷新逻辑
  // 实际的数据刷新会通过@query事件处理
};

onShow(() => {
 queryOrderList()
});

onLoad((options) => {
  console.log(options, "订单详情");
  detailId.value = options.id;
});

// 取消订单
const cancelOrder = (item) => {
  isMergePopup.value.showIsMergePopup(item, "nopay");
};

// 支付订单
const payOrder = (item) => {
  isMergePopup.value.showIsMergePopup(item, "pay");
};

// 删除订单
const delOrder = (item) => {
  http
    .post("/campuspay/mobile/general-pay-order/delete/order", {
      id: item.id,
    })
    .then(() => {
      uni.showToast({
        title: "删除订单成功",
        icon: "none",
        duration: 2000,
      });
    });
};

const cancelRefund = () => {
  http
    .post("/campuspay/mobile/general-pay-order/cancel/apply", {
      id: state.refundDetails.id,
    })
    .then(() => {
      uni.showToast({
        title: "取消申请成功",
        icon: "none",
        duration: 2000,
      });
    });
};

const delRefundDetails = () => {
  http
    .post("/campuspay/mobile/general-pay-order/delete/refund", {
      id: state.refundDetails.id,
    })
    .then(() => {
      uni.showToast({
        title: "删除记录成功",
        icon: "none",
        duration: 2000,
      });
    });
};

const refund = (item) => {
  uni.navigateTo({
    url: `/pages/my/refund?id=${item.id}`,
  });
};
onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/orderRecord",
  });
  return true;
});
</script>

<style scoped>
.order-record-page {
  min-height: 100vh;
  background-color: #f9faf9;
  padding-bottom: env(safe-area-inset-bottom);
}

.bottomBtn {
  background-color: #ffffff;
  height: 100rpx;
  padding: 20rpx;
  border-top: 2rpx solid #ebebeb;
  display: flex;
  gap: 30rpx;
  justify-content: flex-end;
}

.cancelBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  border: 2rpx solid #d1d1d1;
  font-weight: 400;
  font-size: 24rpx;
  color: #d1d1d1;
  background: #ffffff;
}

.payBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 148rpx;
  height: 48rpx;
  border-radius: 10rpx;
  background: #11c685;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
}

.order-card {
  background: #ffffff;
}

.order-card_box {
  padding: 32rpx;
}
.payee_order-card {
  background: #ffffff;
  padding: 32rpx;
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 34rpx;
}

.order-type {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.type-icon {
  width: 6rpx;
  height: 24rpx;
  background: #00b781;
  border-radius: 3rpx;
}

.type-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 订单详情 */
.order-details {
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
}

.payee_order-details {
  margin-bottom: 32rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.payee_detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
}

.payee_detail-label {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.payee_detail-value {
  font-weight: 600;
  font-size: 24rpx;
  color: #000000;
  text-align: right;
}

/* z-paging组件样式覆盖 */
:deep(.zp-paging-container) {
  background-color: #ffffff;
}

:deep(.zp-paging-container-content) {
  background-color: #ffffff;
}

.popup-content {
  height: 660rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.handle-card {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.handle-card-close {
  padding: 30rpx;
}

.card-List {
  padding-left: 60rpx;
  padding-right: 60rpx;
}
.cardItem {
  display: flex;
  padding-bottom: 30rpx;
  align-items: center;
  justify-content: space-between;
}

.bottom_btn {
  padding-left: 60rpx;
  padding-right: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bottom_del_btn {
  background: #ffffff;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #dfdfdf;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  width: 172rpx;
  height: 70rpx;
}

.bgLine {
  height: 20rpx;
  background: #f6f5f8;
}

.amount {
  font-weight: 600;
  font-size: 40rpx;
  color: #333333;
}

.order-title {
  font-weight: 600;
  font-size: 36rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}

.order-time {
  font-weight: 600;
  font-size: 24rpx;
  color: #4d4d4d;
  margin-bottom: 32rpx;
}
</style>
