<template>
  <PayPasswordSetting
    @success="onSetPasswordSuccess"
    @failed="onSetPasswordFailed"
    @cancel="onSetPasswordCancel"
    @back="onSetPasswordBack"
  />
</template>

<script setup>
import { ref, onMounted } from "vue";
import PayPasswordSetting from "@/components/PayPasswordSetting.vue";
import{ updateLoginInfoProperty }from "@/utils/loginInfo";
/**
 * 设置成功回调
 * @param {string} password - 设置的密码
 */
const onSetPasswordSuccess = (password) => {
  console.log("支付密码设置成功:", password);

  uni.showToast({
    title: "支付密码设置成功",
    icon: "success",
    duration: 2000,
  });

  updateLoginInfoProperty("isPassword", true);
  // 检查是否从添加银行卡页面跳转过来
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};

  if (options.from === "addBankCard") {
    // 如果是从添加银行卡页面跳转过来，返回时带上设置成功的标记
    setTimeout(() => {
      uni.redirectTo({
        url: "/pages/my/addBankCard",
        success: () => {
          // 通过事件通知添加银行卡页面密码设置成功
          uni.$emit("payPasswordSetSuccess");
        },
      });
    }, 2000);
  } else if (options.from === "setting") {
    setTimeout(() => {
      uni.redirectTo({
        url: "/pages/my/setting",
      });
    }, 2000);
  } else {
    // 其他情况正常返回
    setTimeout(() => {
      uni.navigateBack();
    }, 2000);
  }
};

/**
 * 设置失败回调
 * @param {string} errorMessage - 错误信息
 */
const onSetPasswordFailed = (errorMessage) => {
  console.log("支付密码设置失败:", errorMessage);
  uni.showToast({
    title: errorMessage,
    icon: "none",
    duration: 2000,
  });
};

/**
 * 取消设置回调
 */
const onSetPasswordCancel = () => {
  console.log("用户取消设置支付密码");
  uni.navigateBack();
};

/**
 * 返回回调
 */
const onSetPasswordBack = () => {
  console.log("用户点击返回");
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
/* 这个页面直接使用组件，不需要额外样式 */
</style>
