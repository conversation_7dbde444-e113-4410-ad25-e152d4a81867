<template>
  <view class="bank-list-page">
    <!-- z-paging组件，开启下拉刷新，不开启上拉加载 -->
    <z-paging
      ref="pagingRef"
      v-model="bankCardList"
      :fixed="true"
      :auto="true"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :loading-more-enabled="false"
      @query="queryBankCardList"
      @refresherrefresh="onRefresh"
    >
      <!-- 银行卡列表 -->
      <view class="bank-card-list">
        <view
          class="bank-card-item"
          v-for="item in bankCardList"
          :key="item.id"
          @click="handleCardClick(item)"
        >
          <!-- 银行卡片 -->
          <view
            class="bank-card"
            :style="{
              backgroundImage: `url(${getBankBackground(item.bankCode)})`,
            }"
          >
            <!-- 银行信息区域 -->
            <view class="bank-info">
              <view class="bank-header">
                <image
                  class="bank-icon"
                  :src="getBankIcon(item.bankCode)"
                  mode="aspectFit"
                />
                <view class="bank-name">{{ getBankName(item.bankCode) }}</view>
              </view>
              <view class="card-type">{{ item.cardType || "储蓄卡" }}</view>
            </view>

            <!-- 卡号信息区域 -->
            <view class="card-info">
              <view class="card-number">
                <text class="card-dots">•••• •••• ••••</text>
                <text class="card-last-four">{{
                  item.cardNumber.slice(-4)
                }}</text>
              </view>
              <view class="card-actions">
                <view class="unbind-btn" @click.stop="handleUnbind(item)">
                  解绑
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
      </view>

      <!-- 底部固定区域：操作按钮 -->
      <template #bottom>
        <view class="bottom-section">
          <view class="button-row">
            <view class="action-btn primary" @click="handleAddBank">
              添加银行卡
            </view>
          </view>
        </view>
      </template>
    </z-paging>

    <!-- 支付密码验证弹窗 -->
    <PayPasswordVerify
      v-model:visible="showPasswordVerify"
      :verify-function="verifyPayPassword"
      @success="onPasswordVerifySuccess"
      @failed="onPasswordVerifyFailed"
      @cancel="onPasswordVerifyCancel"
    />
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import PayPasswordVerify from "@/components/PayPasswordVerify.vue";
import { useSafeStorage } from "@/hooks/useSafeStorage";
const loginInfo = useSafeStorage("checkIn-loginInfo", {});
import { bankConfig } from "./bankConfig";
import RSA from "@/utils/rsa";
import http from "@/utils/request";

// import { getBankCardList, unbindBankCard, verifyPayPasswordAPI } from '@/api/index.js'

// 响应式数据
const pagingRef = ref(null);
const bankCardList = ref([]);
const loading = ref(false);
const showPasswordVerify = ref(false);
const currentUnbindCard = ref(null);

/**
 * 根据银行代码获取银行背景图
 * @param {string} bankCode - 银行代码
 * @returns {string} 背景图路径
 */
const getBankBackground = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.background;
};

/**
 * 根据银行代码获取银行图标
 * @param {string} bankCode - 银行代码
 * @returns {string} 图标路径
 */
const getBankIcon = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.icon;
};

/**
 * 根据银行代码获取银行名称
 * @param {string} bankCode - 银行代码
 * @returns {string} 银行名称
 */
const getBankName = (bankCode) => {
  const config = bankConfig[bankCode] || bankConfig["DEFAULT"];
  return config.name;
};

const queryBankCardList = async () => {
  try {
    loading.value = true;
    const res = await http.post(
      "/app/enrollment/bocom/bank-card/list-sign-card",
      {
        reportId: loginInfo.value.reportId || null,
      }
    );
    bankCardList.value = res.data || [];

    pagingRef.value?.complete(bankCardList.value);
  } catch (error) {
    console.error("获取银行卡列表失败:", error);
    uni.showToast({
      title: "获取银行卡列表失败",
      icon: "none",
    });
    pagingRef.value?.complete([]);
  } finally {
    loading.value = false;
  }
};

/**
 * 下拉刷新处理
 */
const onRefresh = () => {
  queryBankCardList();
};

/**
 * 银行卡点击处理
 * @param {object} item - 银行卡信息
 */
const handleCardClick = (item) => {
  console.log("点击银行卡:", item);
  // 可以在这里添加查看银行卡详情的逻辑
};

/**
 * 解绑银行卡
 * @param {object} item - 银行卡信息
 */
const handleUnbind = (item) => {
  // 保存当前要解绑的银行卡信息
  currentUnbindCard.value = item;
  // 显示支付密码验证弹窗
  showPasswordVerify.value = true;
};

/**
 * 支付密码验证函数
 * @param {string} password - 输入的支付密码
 * @returns {Promise<boolean>} 验证结果
 */
const verifyPayPassword = async (password) => {
  try {
    const paramEncipher = {
      reportId: loginInfo.value.reportId || null,
      relatedId: currentUnbindCard.value.relatedId,
      password: password,
    };

    // 银行卡解除绑定校验
    const res = await http.post(
      "/app/enrollment/bocom/bank-card/card-unbind-validate",
      {
        paramEncipher: RSA.encrypt(JSON.stringify(paramEncipher)),
      }
    );

    return res.data;
  } catch (error) {
    console.error("支付密码验证失败:", error);
    return false;
  }
};

/**
 * 支付密码验证成功回调
 * @param {string} password - 验证成功的密码
 */
const onPasswordVerifySuccess = (password) => {
  console.log("支付密码验证成功:", password);
  // 执行银行卡解绑操作
  if (currentUnbindCard.value) {
    // 验证支付密码成功跳去接绑定的页面
    uni.navigateTo({
      url: `/pages/my/unbind?relatedId=${currentUnbindCard.value.relatedId}`,
    });
  }
};

/**
 * 支付密码验证失败回调
 * @param {string} errorMessage - 错误信息
 */
const onPasswordVerifyFailed = (errorMessage) => {
  console.log("支付密码验证失败:", errorMessage);
  uni.showToast({
    title: errorMessage,
    icon: "none",
  });
};

/**
 * 支付密码验证取消回调
 */
const onPasswordVerifyCancel = () => {
  console.log("用户取消支付密码验证");
  currentUnbindCard.value = null;
};

/**
 * 添加银行卡
 */
const handleAddBank = () => {
  // 跳转到添加银行卡页面
  uni.navigateTo({
    url: "/pages/my/addBankCard",
  });
};

// 页面挂载时的初始化
onMounted(() => {
  console.log("银行卡列表页面已挂载");
});

// 全部返回卡列表页面
onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/homePage",
  });
  return true;
});
</script>

<style lang="scss" scoped>
.bank-list-page {
  height: 100vh;
  background-color: #f7f7f7;
}

.bank-card-list {
  padding: 32rpx 32rpx 0;
}

.bank-card-item {
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 银行卡片样式 */
.bank-card {
  height: 224rpx;
  border-radius: 16rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  /* 添加渐变遮罩以确保文字可读性 */
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 1;
  }

  /* 确保内容在遮罩之上 */
  .bank-info,
  .card-info {
    position: relative;
    z-index: 2;
  }
}

/* 银行信息区域 */
.bank-info {
  padding: 32rpx 32rpx 0 32rpx;
  .bank-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rpx;

    .bank-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    .bank-name {
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;
    }
  }

  .card-type {
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    margin-left: 64rpx;
  }
}

/* 卡号信息区域 */
.card-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0rpx 24rpx 28rpx 92rpx;

  .card-number {
    display: flex;
    align-items: center;

    .card-dots {
      color: #ffffff;
      font-size: 28rpx;
      margin-right: 16rpx;
      letter-spacing: 4rpx;
    }

    .card-last-four {
      color: #ffffff;
      font-size: 48rpx;
      font-weight: 400;
    }
  }

  .card-actions {
    .unbind-btn {
      background-color: rgba(255, 255, 255, 0.5);
      color: #ffffff;
      font-size: 24rpx;
      padding: 12rpx 24rpx;
      border-radius: 8rpx;

      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

/* 底部固定区域样式 */
.bottom-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;

  .button-row {
    display: flex;
    gap: 24rpx;

    .action-btn {
      flex: 1;
      height: 92rpx;

      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-weight: 400;
      font-size: 32rpx;
      color: #ffffff;

      &.primary {
        background: #11c685;
        border-radius: 10rpx;

        color: #ffffff;

        &:active {
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
