<template>
  <view class="informationPage">
    <view class="bgLine"></view>
    <view class="listPage">
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">姓名：</view>
          <view>{{ loginInfo.name }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">性别：</view>
          <view>{{ genderText }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">手机号码：</view>
          <view>{{ loginInfo.phone }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 报到专业：</view>
          <view>{{ loginInfo.enrollmentMajorName }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 班级名称：</view>
          <view>{{ loginInfo.classesName || "-" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 宿舍号：</view>
          <view>{{ loginInfo.dormitoryCode || "-" }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useSafeStorage } from "@/hooks/useSafeStorage";

const loginInfo = useSafeStorage("checkIn-loginInfo", {});

console.log(loginInfo, "loginInfo");

const genderText = computed(() => {
  return loginInfo.value.gender === 1 ? "男" : "女";
});
</script>

<style scoped>
.informationPage {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.listPage {
  background: #ffffff;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.listItem {
  padding: 30rpx 0rpx 30rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e8e8e8;
}

.listItem_name {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  display: flex;
  align-items: center;
}

.item_title {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  padding-left: 16rpx;
  width: 140rpx;
}

.bgLine {
  height: 20rpx;
  background: #f6f6f6;
}
</style>
