<template>
  <view class="identity-container">
    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 手机号输入框 -->
      <view class="input-section">
        <view class="input-wrapper">
          <input
            v-model="phoneNumber"
            type="number"
            placeholder="请输入手机号"
            class="phone-input"
            maxlength="11"
            @input="onPhoneInput"
          />
        </view>
      </view>

      <!-- 验证码输入框 -->
      <view class="input-section">
        <view class="input-wrapper verification-wrapper">
          <input
            v-model="verificationCode"
            type="number"
            placeholder="请输入验证码"
            class="verification-input"
            maxlength="6"
            @input="onCodeInput"
          />
          <button
            class="get-code-btn"
            :class="{
              active: canSendCode,
              disabled: !canSendCode || countdown > 0,
            }"
            :disabled="!canSendCode || countdown > 0"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s` : "获取验证码" }}
          </button>
        </view>
      </view>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bottom-button">
      <button
        class="next-btn"
        :class="{ active: canSubmit, disabled: !canSubmit }"
        :disabled="!canSubmit"
        @click="handleNext"
      >
        下一步
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted } from "vue";
import http from "@/utils/request";

// 响应式数据
const phoneNumber = ref("");
const verificationCode = ref("");
const countdown = ref(0);
let countdownTimer = null;

// 计算属性
const canSendCode = computed(() => {
  // 验证手机号格式（11位数字，以1开头）
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phoneNumber.value);
});

const canSubmit = computed(() => {
  return canSendCode.value && verificationCode.value.length === 6;
});

// 方法
const onPhoneInput = (e) => {
  // 限制只能输入数字
  phoneNumber.value = e.detail.value.replace(/\D/g, "");
};

const onCodeInput = (e) => {
  // 限制只能输入数字
  verificationCode.value = e.detail.value.replace(/\D/g, "");
};

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value || countdown.value > 0) {
    return;
  }

  try {
    uni.showLoading({
      title: "发送中...",
      mask: true,
    });

    const response = await http.post("/app/enrollment/sms/otc/send", {
      phone: phoneNumber.value,
    });

    uni.hideLoading();

    if (response.code === 0) {
      uni.showToast({
        title: "验证码已发送",
        icon: "success",
        duration: 2000,
      });

      // 开始倒计时
      startCountdown();
    } else {
      uni.showToast({
        title: response.message || "发送失败",
        icon: "error",
        duration: 2000,
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error("发送验证码失败:", error);
    uni.showToast({
      title: "发送失败，请重试",
      icon: "error",
      duration: 2000,
    });
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }, 1000);
};

// 处理下一步
const handleNext = async () => {
  if (!canSubmit.value) {
    return;
  }

  try {
    uni.showLoading({
      title: "验证中...",
      mask: true,
    });

    const response = await http.post("/app/enrollment/sms/otc/verify", {
      phone: phoneNumber.value,
      smsCode: verificationCode.value,
    });

    uni.hideLoading();

    if (response.code === 0) {
      uni.showToast({
        title: "验证成功",
        icon: "success",
        duration: 2000,
      });

      uni.navigateTo({
        url: "/pages/my/payPasswordSetting?from=setting",
      });
    } else {
      uni.showToast({
        title: response.message || "验证失败",
        icon: "error",
        duration: 2000,
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error("验证失败:", error);
    uni.showToast({
      title: "验证失败，请重试",
      icon: "error",
      duration: 2000,
    });
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.identity-container {
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
}

.content {
  flex: 1;
  padding: 60rpx 40rpx 0;
}

.input-section {
  margin-bottom: 60rpx;
}

.input-wrapper {
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}

.verification-wrapper {
  display: flex;
  align-items: center;
}

.phone-input,
.verification-input {
  width: 100%;
  height: 80rpx;
  font-size: 32rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
  padding: 0;
}

.verification-input {
  flex: 1;
  margin-right: 20rpx;
}

.phone-input::placeholder,
.verification-input::placeholder {
  color: #bfbfbf;
  font-size: 32rpx;
  font-weight: 400;
}

.get-code-btn {
  flex-shrink: 0;
  width: 180rpx;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #ffffff;
  background-color: #dadada;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.get-code-btn.active {
  background-color: #00b781;
  color: #ffffff;
}

.get-code-btn.disabled {
  background-color: #dadada;
  color: #ffffff;
}

.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.next-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  background-color: #dadada;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.next-btn.active {
  background-color: #00b781;
  color: #ffffff;
}

.next-btn.disabled {
  background-color: #dadada;
  color: #ffffff;
}

/* 适配安全区域 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .bottom-button {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}
</style>
