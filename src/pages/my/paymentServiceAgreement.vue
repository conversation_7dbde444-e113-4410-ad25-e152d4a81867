<template>
  <view class="payment-agreement-page">
    <!-- 协议标题 -->
    <view class="agreement-header">
      <text class="agreement-title">{{ agreementData.title }}</text>
    </view>

    <!-- 协议内容 -->
    <view class="agreement-content">
      <view
        v-for="(section, index) in agreementData.sections"
        :key="index"
        class="agreement-section"
        :class="section.type"
      >
        <!-- 警告类型 -->
        <view v-if="section.type === 'warning'" class="warning-section">
          <text class="section-title warning-title">{{ section.title }}</text>
          <text class="section-content">{{ section.content }}</text>
          <view v-if="section.items" class="section-items">
            <text
              v-for="(item, itemIndex) in section.items"
              :key="itemIndex"
              class="section-item"
            >
              {{ item }}
            </text>
          </view>
        </view>

        <!-- 提示类型 -->
        <view v-else-if="section.type === 'notice'" class="notice-section">
          <text class="section-title notice-title">{{ section.title }}</text>
          <text class="section-content notice-content">{{ section.content }}</text>
        </view>

        <!-- 普通章节 -->
        <view v-else class="normal-section">
          <text class="section-title">{{ section.title }}</text>
          <text v-if="section.content" class="section-content">{{ section.content }}</text>
          <view v-if="section.items" class="section-items">
            <text
              v-for="(item, itemIndex) in section.items"
              :key="itemIndex"
              class="section-item"
            >
              {{ item }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="agreement-footer">
      <button class="confirm-btn" @click="handleConfirm">
        我已阅读并同意
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";

// 协议数据
const agreementData = ref({});

/**
 * 加载协议数据
 */
const loadAgreementData = async () => {
  try {
    // 从静态资源加载协议JSON文件
    const response = await uni.request({
      url: "/static/agreement/payment-service-agreement.json",
      method: "GET",
    });
    
    if (response.statusCode === 200) {
      agreementData.value = response.data;
    } else {
      console.error("加载协议数据失败:", response);
      // 如果加载失败，使用默认数据
      setDefaultAgreementData();
    }
  } catch (error) {
    console.error("加载协议数据出错:", error);
    // 如果出错，使用默认数据
    setDefaultAgreementData();
  }
};

/**
 * 设置默认协议数据（备用方案）
 */
const setDefaultAgreementData = () => {
  agreementData.value = {
    title: "快捷支付服务协议",
    sections: [
      {
        type: "warning",
        title: "【审慎阅读】",
        content: "您在点击同意前，请您务必审慎阅读、充分理解协议中相关条款内容。",
        items: []
      }
    ]
  };
};

/**
 * 确认按钮处理
 */
const handleConfirm = () => {
  // 返回上一页
  uni.navigateBack({
    success: () => {
      console.log("返回上一页成功");
    },
    fail: (error) => {
      console.error("返回失败:", error);
    }
  });
};

// 页面挂载时加载协议数据
onMounted(() => {
  loadAgreementData();
});
</script>

<style lang="scss" scoped>
.payment-agreement-page {
  min-height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 协议头部 */
.agreement-header {
  background-color: #ffffff;
  padding: 40rpx 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .agreement-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    text-align: center;
    display: block;
  }
}

/* 协议内容 */
.agreement-content {
  flex: 1;
  background-color: #ffffff;
  padding: 0 30rpx 200rpx;

  .agreement-section {
    margin-bottom: 40rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.6;
      display: block;
      margin-bottom: 20rpx;
    }

    .section-content {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.8;
      display: block;
      margin-bottom: 16rpx;
      white-space: pre-line;
    }

    .section-items {
      .section-item {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.8;
        display: block;
        margin-bottom: 12rpx;
        white-space: pre-line;
      }
    }

    /* 警告类型样式 */
    &.warning {
      .warning-title {
        color: #ff4757;
        font-weight: 700;
      }
    }

    /* 提示类型样式 */
    &.notice {
      .notice-title {
        color: #00b781;
        font-weight: 700;
      }

      .notice-content {
        background-color: #f8f9fa;
        padding: 20rpx;
        border-radius: 8rpx;
        border-left: 6rpx solid #00b781;
      }
    }
  }
}

/* 底部按钮 */
.agreement-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .confirm-btn {
    width: 100%;
    height: 92rpx;
    background: #11c685;
    border-radius: 10rpx;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border: none;

    &:active {
      background-color: #00b781;
    }
  }
}
</style>
