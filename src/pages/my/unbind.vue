<template>
  <view class="unbind-container">
    <!-- 主要内容区域 -->
    <view class="content">
      <view class="bgLine"></view>

      <view class="cardInfo">
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">姓名</text>
          </view>
          <view class="card_value">{{ state.cardDetail.accountName }}</view>
        </view>
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">身份证号码</text>
          </view>
          <view class="card_value">{{ state.cardDetail.certNo }}</view>
        </view>
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">开户银行</text>
          </view>
          <view class="card_value">{{ state.cardDetail.bankName }}</view>
        </view>
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">银行卡类型</text>
          </view>
          <view class="card_value">{{ state.cardDetail.accountType }}</view>
        </view>
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">银行卡号</text>
          </view>
          <view class="card_value">{{ state.cardDetail.accountNo }}</view>
        </view>
        <view class="card_item">
          <view class="card_label">
            <text class="card_label_red">*</text>
            <text class="card_label_text">手机号码</text>
          </view>
          <view class="card_value">{{ state.cardDetail.mobile }}</view>
        </view>
      </view>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bottom-button">
      <button class="next-btn" @click="unBindOk">解绑</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onUnmounted } from "vue";
import http from "@/utils/request";
import RSA from "@/utils/rsa";
import { onLoad } from "@dcloudio/uni-app";
import { useSafeStorage } from "@/hooks/useSafeStorage";
const loginInfo = useSafeStorage("checkIn-loginInfo", {});
const relatedId = ref("");

const state = reactive({
  cardDetail: {},
});

const unBindOk = () => {
  uni.showModal({
    title: "确定解绑",
    content: "确定解绑该银行卡吗？解绑后不能用此银行卡进行充值、缴费等",
    cancelText: "否",
    confirmText: "是",
    confirmColor: "#00B781",
    cancelColor: "#00B781",

    success: (res) => {
      if (res.confirm) {
        const paramEncipher = {
          reportId: loginInfo.value.reportId || null, // 报到人员id
          relatedId: relatedId.value, // 签约卡id
        };
        http
          .post("/app/enrollment/bocom/bank-card/card-unbind", {
            paramEncipher: RSA.encrypt(JSON.stringify(paramEncipher)),
          })
          .then((res) => {
            uni.navigateTo({
              url: "/pages/my/bankList",
            });
          });
      }
    },
  });
};

onBackPress(() => {
  uni.navigateTo({
    url: "/pages/my/bankList",
  });
  return true;
});

const cardDetail = () => {
  http
    .post("/app/enrollment/bocom/bank-card/card-detail", {
      relatedId: relatedId.value,
      reportId: loginInfo.value.reportId || null,
    })
    .then((res) => {
      console.log(res, "银行卡详情");
      state.cardDetail = res.data || {};
    });
};
onLoad((options) => {
  relatedId.value = options.relatedId;
  cardDetail();
});
</script>

<style lang="scss" scoped>
.unbind-container {
  display: flex;
  flex-direction: column;
  position: relative;
  background: #f9faf9;
  height: calc(100vh - var(--window-top));
}

.cardInfo {
  padding-left: 30rpx;
  padding-right: 30rpx;
  background-color: #ffffff;
}

.bgLine {
  height: 20rpx;
  background: #f9faf9;
}

.content {
  flex: 1;
}

.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}

.next-btn {
  width: 100%;
  height: 92rpx;
  border-radius: 10rpx;
  color: #ffffff;
  background-color: #f5222d;
  border: none;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-weight: 400;
  font-size: 32rpx;
}

.card_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.card_label {
  display: flex;
  align-items: center;
}
.card_label_red {
  color: #f5222d;
  font-size: 28rpx;
  margin-right: 4rpx;
}
.card_label_text {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

.card_value {
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
}

/* 适配安全区域 */
@supports (bottom: env(safe-area-inset-bottom)) {
  .bottom-button {
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }
}
</style>
