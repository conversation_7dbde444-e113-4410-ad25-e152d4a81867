export function updateLoginInfoProperty(property, value) {
  let loginInfoStr = uni.getStorageSync("checkIn-loginInfo");
  if (!loginInfoStr) {
    // 如果不存在，可以选择初始化一个对象
    let initObj = {
      classesName: "",
      dormitoryCode: "",
      enrollmentMajorId: null,
      enrollmentMajorName: "",
      gender: null,
      isPassword: false,
      name: "",
      password: "",
      paymentStatus: null,
      phone: "",
      planId: null,
      planName: "",
      reportId: null,
      schoolId: null,
      schoolName: "",
    }; // 或其他默认值
    uni.setStorageSync("checkIn-loginInfo", JSON.stringify(initObj));
    return;
  }

  let loginInfoObj = JSON.parse(loginInfoStr);
  loginInfoObj[property] = value;
  uni.setStorageSync("checkIn-loginInfo", JSON.stringify(loginInfoObj));
}
