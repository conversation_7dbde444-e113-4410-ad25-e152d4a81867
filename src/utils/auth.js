
const prefix = `user`
const key = `${prefix}-token`

export function getToken() {
    return uni.getStorageSync(key)
}

export function setToken(token) {
    uni.setStorageSync(key, token)
}

export function removeToken() {
    uni.removeStorageSync(key)
}

export function setRefreshToken(token) {
    uni.setStorageSync(`${prefix}-refresh-token`, token)
}

export function getRefreshToken(token) {
    uni.setStorageSync(`${prefix}-refresh-token`, token)
}

export function removeRefreshToken(token) {
    uni.setStorageSync(`${prefix}-refresh-token`, token)
}

export function toLogin() {
    uni.redirectTo({
        url: "/pages/login/index"
    })
}

export function isLogin() {
    return getToken() != '' && getToken() != null && getToken() != undefined
}

// 微信相关工具函数
export function getOpenId() {
    return uni.getStorageSync('wechat-openid')
}

export function setOpenId(openid) {
    uni.setStorageSync('wechat-openid', openid)
}

export function removeOpenId() {
    uni.removeStorageSync('wechat-openid')
}