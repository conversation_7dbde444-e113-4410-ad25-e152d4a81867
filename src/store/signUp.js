import { defineStore } from "pinia";

/**
 * 表单数据管理 Store
 * 用于管理个人信息、表单数据的持久化存储
 */
const useSignUpStore = defineStore("signUp", {
  state: () => {
    return {
      // 个人详细信息表单数据
      personalInfo: {
        name: "", // 姓名 *
        gender: "", // 性别 *
        phone: "", // 手机号 *
        email: "", // 邮箱
        idCard: "", // 身份证号
        birthday: "", // 出生日期
        politicalStatus: "", // 政治面貌
        nation: "", // 民族
        nativePlaces: [], // 籍贯
        nativePlace: "", // 籍贯
        householdAddress: "", // 户籍所在地
        isNonlocalStudent: "", // 是否外地学籍 *
        graduationSchool: "", // 毕业院校 *
        address: "", // 家庭现住址
        totalScore: "", // 考试总分
      },
    };
  },

  getters: {
    /**
     * 获取个人信息表单数据
     */
    getPersonalInfo(state) {
      return state.personalInfo;
    },

    /**
     * 检查个人信息是否填写完整（必填项）
     */
    isPersonalInfoValid(state) {
      const { name, gender, phone } = state.personalInfo;
      return name.trim() !== "" && gender !== "" && phone.trim() !== "";
    },
  },

  actions: {
    /**
     * 更新个人信息表单数据
     * @param {Object} personalInfo - 个人信息对象
     */
    updatePersonalInfo(personalInfo) {
      this.personalInfo = { ...this.personalInfo, ...personalInfo };
    },

    /**
     * 重置个人信息表单数据
     */
    resetPersonalInfo() {
      this.personalInfo = {
        name: "",
        gender: "",
        phone: "",
        email: "",
        idCard: "",
        birthday: "",
        politicalStatus: "",
        nation: "",
        nativePlaces: [],
        nativePlace: "",
        householdAddress: "",
        address: "",
        totalScore: "",
      };
    },

    /**
     * 清空所有表单数据
     */
    clearAllData() {
      this.resetPersonalInfo();
    },
  },

  // 配置持久化存储
  persist: {
    key: "signUp",
    paths: ["personalInfo"], // 指定需要持久化的状态
  },
});

export default useSignUpStore;
