import { defineStore } from 'pinia';

/**
 * 报名表单数据管理 Store
 * 用于管理个人信息、家庭信息、上传附件等表单数据的持久化存储
 */
const useCheckInStore = defineStore('checkIn', {
    state: () => {
        return {
            // 个人详细信息表单数据
            personalInfo: {
                name: "", // 姓名 *
                gender: "", // 性别 *
                phone: "", // 手机号 *
                email: "", // 邮箱
                idType:"", // 证件类型
                idCard: "", // 证件证号
                birthday: "", // 出生日期 *
                politicalStatus: "", // 政治面貌 *
                nation: "", // 民族 *
                nativePlaces: [], // 籍贯
                nativePlace: "", // 籍贯
                householdAddress: "", // 户籍所在地 *
                isNonlocalStudent: "", // 是否外地学籍 *
                graduationSchool: "", // 毕业院校 *
                address: "", // 家庭现住址 *
                totalScore: "", // 考试总分 *
                isDormitory: "", // 是否住校 *
                dormitoryId: "", // 宿舍类型（住校时必填）
            },
            
            // 家庭成员信息数组
            familyInfo: [
                {
                    name: "", // 姓名
                    relations: "", // 家庭关系
                    phone: "", // 手机号
                },
                {
                    name: "",
                    relations: "",
                    phone: "",
                },
            ],
            
            // 上传附件信息数组
            uploadItems: [
                {
                    title: "人脸照片",
                    value: "faceUrl",
                    type: "standard", // 标准正方形样式
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "人像面：",
                    value: "idCardFrontUrl",
                    type: "id", // 身份证样式
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "国徽面：",
                    value: "idCardBackUrl",
                    type: "id", // 身份证样式
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "成绩单附件",
                    value: "transcriptUrl",
                    type: "standard", // 标准正方形样式
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "毕业照附件",
                    value: "diplomaUrl",
                    type: "standard", // 标准正方形样式
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
            ],
        };
    },
    
    getters: {
        /**
         * 获取个人信息表单数据
         */
        getPersonalInfo(state) {
            return state.personalInfo;
        },
        
        /**
         * 获取家庭成员信息
         */
        getFamilyInfo(state) {
            return state.familyInfo;
        },
        
        /**
         * 获取上传附件信息
         */
        getUploadItems(state) {
            return state.uploadItems;
        },
        
        /**
         * 检查个人信息是否填写完整（必填项）
         */
        isPersonalInfoValid(state) {
            const { name, gender, phone } = state.personalInfo;
            return name.trim() !== '' && gender !== '' && phone.trim() !== '';
        },
        
        /**
         * 获取已上传的附件数据（用于提交）
         */
        getUploadedAttachments(state) {
            return state.uploadItems.reduce((acc, curr) => {
                if (curr.imageUrl) {
                    acc[curr.value] = curr.imageUrl;
                }
                return acc;
            }, {});
        },
    },
    
    actions: {
        /**
         * 更新个人信息表单数据
         * @param {Object} personalInfo - 个人信息对象
         */
        updatePersonalInfo(personalInfo) {
            this.personalInfo = { ...this.personalInfo, ...personalInfo };
        },
        
        /**
         * 重置个人信息表单数据
         */
        resetPersonalInfo() {
            this.personalInfo = {
                name: "",
                gender: "",
                phone: "",
                email: "",
                idCard: "",
                birthday: "",
                politicalStatus: "",
                nation: "",
                nativePlaces: [],
                nativePlace: "",
                householdAddress: "",
                address: "",
                totalScore: "",
                isDormitory: "",
                dormitoryId: "",
            };
        },
        
        /**
         * 更新家庭成员信息
         * @param {Array} familyInfo - 家庭成员信息数组
         */
        updateFamilyInfo(familyInfo) {
            this.familyInfo = [...familyInfo];
        },
        
        /**
         * 添加新的家庭成员
         */
        addFamilyMember() {
            this.familyInfo.push({
                name: "",
                relations: "",
                phone: "",
            });
        },
        
        /**
         * 删除指定的家庭成员
         * @param {number} index - 要删除的成员索引
         */
        removeFamilyMember(index) {
            if (index >= 0 && index < this.familyInfo.length) {
                this.familyInfo.splice(index, 1);
            }
        },
        
        /**
         * 重置家庭成员信息
         */
        resetFamilyInfo() {
            this.familyInfo = [
                {
                    name: "",
                    relations: "",
                    phone: "",
                },
                {
                    name: "",
                    relations: "",
                    phone: "",
                },
            ];
        },
        
        /**
         * 更新上传附件信息
         * @param {Array} uploadItems - 上传附件信息数组
         */
        updateUploadItems(uploadItems) {
            this.uploadItems = [...uploadItems];
        },
        
        /**
         * 更新指定附件的信息
         * @param {number} index - 附件索引
         * @param {Object} itemData - 要更新的附件数据
         */
        updateUploadItem(index, itemData) {
            if (index >= 0 && index < this.uploadItems.length) {
                this.uploadItems[index] = { ...this.uploadItems[index], ...itemData };
            }
        },
        
        /**
         * 重置上传附件信息
         */
        resetUploadItems() {
            this.uploadItems = [
                {
                    title: "人脸照片",
                    value: "faceUrl",
                    type: "standard",
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "人像面：",
                    value: "idCardFrontUrl",
                    type: "id",
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "国徽面：",
                    value: "idCardBackUrl",
                    type: "id",
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "成绩单附件",
                    value: "transcriptUrl",
                    type: "standard",
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
                {
                    title: "毕业照附件",
                    value: "diplomaUrl",
                    type: "standard",
                    imageUrl: "",
                    error: "",
                    uploading: false,
                },
            ];
        },
        
        /**
         * 清空所有表单数据
         */
        clearAllData() {
            this.resetPersonalInfo();
            this.resetFamilyInfo();
            this.resetUploadItems();
        },
    },

    // 配置持久化存储
    persist: {
        key: "checkIn",
        paths: ['personalInfo', 'familyInfo', 'uploadItems'], // 指定需要持久化的状态
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore checkIn store '${ctx.store.$id}'`);
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore checkIn store '${ctx.store.$id}'`);
        }
    }
});

export default useCheckInStore;
