import { defineStore } from "pinia";

const useUserStore = defineStore("user", {
  state: () => {
    return {
      user: {},
    };
  },
  getters: {},
  actions: {},

  persist: {
    key: "user",
    paths: ["user"],
    beforeRestore: (ctx) => {
      console.log(`beforeRestore '${ctx.store.$id}'`);
    },
    afterRestore: (ctx) => {
      console.log(`afterRestore '${ctx.store.$id}'`);
    },
  },
});

export default useUserStore;
